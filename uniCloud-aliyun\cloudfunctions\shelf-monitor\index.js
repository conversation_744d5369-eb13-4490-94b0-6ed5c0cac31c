'use strict'
// {{ AURA-X: Modify - 移除不再使用的直接数据库操作，统一使用DatabaseManager. Approval: 寸止(ID:**********). }}
// {{ AURA-X: Modify - 使用公共模块替代重复代码. Approval: 寸止(ID:1735372800). }}
const { PlatformAdapterFactory, Logger, AlertManager, DatabaseManager, getStateText } = require('shelf-core')
/**
 * 货架监控云函数
 * 功能：
 * 1. 定时更新各平台货架状态
 * 2. 检测账号出租状态变化
 * 3. 自动执行上下架操作
 * 4. 记录操作日志
 */
exports.main = async (event, context) => {
  console.log('货架监控任务开始执行', new Date())
  const logger = new Logger()

  // {{ AURA-X: Modify - 简化告警系统，仅保留Server酱. Approval: 寸止(ID:**********). }}
  const alertManager = new AlertManager({
    serverChanKey: process.env.SERVER_CHAN_KEY || ''
  })

  const startTime = Date.now()
  try {
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    // {{ AURA-X: Modify - 修复定时任务获取平台配置的问题，使用getAllPlatformConfigs方法. Approval: 寸止(ID:1735373100). }}
    // 获取所有启用的平台配置
    const dbManager = new DatabaseManager()
    const platformConfigs = await dbManager.getAllPlatformConfigs(null, true)

    if (platformConfigs.length === 0) {
      console.log('没有找到启用的平台配置')
      return {
        code: 0,
        message: '没有找到启用的平台配置'
      }
    }
    // 按用户分组处理
    const userGroups = {}
    platformConfigs.forEach(config => {
      if (!userGroups[config.user_id]) {
        userGroups[config.user_id] = []
      }
      userGroups[config.user_id].push(config)
    })
    // 处理每个用户的平台配置
    for (const userId in userGroups) {
      await processUserPlatforms(userId, userGroups[userId], logger)
    }
    // 清理过期日志（保留1天）
    await cleanupExpiredLogs()
    const executionTime = Date.now() - startTime
    console.log(`货架监控任务执行完成，耗时: ${executionTime}ms`)
    return {
      code: 0,
      message: '货架监控任务执行成功',
      executionTime
    }
  } catch (error) {
    console.error('货架监控任务执行失败:', error)

    // {{ AURA-X: Add - 系统异常告警. Approval: 寸止(ID:**********). }}
    await alertManager.alertSystemError(error, 'shelf-monitor', {
      executionTime: Date.now() - startTime,
      triggerType: 'auto'
    })

    // 记录系统错误日志
    await logger.log({
      user_id: 'system',
      platform_type: 'system',
      action: 'monitor_task',
      status: 0,
      message: error.message,
      error_code: error.code || 'UNKNOWN_ERROR',
      execution_time: Date.now() - startTime,
      trigger_type: 'auto'
    })
    return {
      code: -1,
      message: '货架监控任务执行失败',
      error: error.message
    }
  }
}
/**
 * 处理单个用户的多个平台配置
 * @param {string} userId 用户ID
 * @param {Array} platforms 平台配置列表
 * @param {Logger} logger 日志记录器
 */
async function processUserPlatforms(userId, platforms, logger) {
  try {
    console.log(`开始处理用户 ${userId} 的 ${platforms.length} 个平台`)
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    // 获取用户的所有活跃货架
    const dbManager = new DatabaseManager()
    const shelfResult = await dbManager.getShelfList(userId, { activeOnly: true, pageSize: 1000 })

    if (shelfResult.list.length === 0) {
      console.log(`用户 ${userId} 没有活跃的货架`)
      return
    }
    // 按游戏账号分组货架
    const accountGroups = {}
    shelfResult.list.forEach(shelf => {
      if (!accountGroups[shelf.game_account]) {
        accountGroups[shelf.game_account] = []
      }
      accountGroups[shelf.game_account].push(shelf)
    })
    // 处理每个游戏账号的多平台货架
    for (const gameAccount in accountGroups) {
      await processAccountShelves(userId, gameAccount, accountGroups[gameAccount], platforms, logger)
    }
  } catch (error) {
    console.error(`处理用户 ${userId} 平台配置失败:`, error)
    await logger.log({
      user_id: userId,
      platform_type: 'system',
      action: 'process_user_platforms',
      status: 0,
      message: error.message,
      trigger_type: 'auto'
    })
  }
}
/**
 * 处理单个游戏账号在多个平台的货架状态
 * @param {string} userId 用户ID
 * @param {string} gameAccount 游戏账号
 * @param {Array} shelves 货架列表
 * @param {Array} platforms 平台配置列表
 * @param {Logger} logger 日志记录器
 */
async function processAccountShelves(userId, gameAccount, shelves, platforms, logger) {
  try {
    console.log(`开始处理账号 ${gameAccount} 的 ${shelves.length} 个货架`)
    // 创建平台适配器映射
    const platformAdapters = {}
    for (const platformConfig of platforms) {
      try {
        const adapter = PlatformAdapterFactory.create(platformConfig.platform_type, platformConfig)
        platformAdapters[platformConfig.platform_type] = adapter
      } catch (error) {
        console.error(`创建平台适配器失败 ${platformConfig.platform_type}:`, error)
        await logger.log({
          user_id: userId,
          platform_type: platformConfig.platform_type,
          action: 'create_adapter',
          status: 0,
          message: error.message,
          trigger_type: 'auto'
        })
      }
    }
    // {{ AURA-X: Modify - 优化货架状态更新，避免重复API调用. Approval: 寸止(ID:**********). }}
    // 先获取各平台的完整货架列表，然后复用数据
    const platformShelfLists = {}
    for (const [platformType, adapter] of Object.entries(platformAdapters)) {
      try {
        console.log(`获取${platformType}平台的完整货架列表`)
        platformShelfLists[platformType] = await adapter.getShelfList()
      } catch (error) {
        console.error(`获取${platformType}平台货架列表失败:`, error)
        await logger.log({
          user_id: userId,
          platform_type: platformType,
          action: 'get_shelf_list',
          status: 0,
          message: error.message,
          trigger_type: 'auto'
        })
      }
    }

    // 更新各平台货架状态
    const currentStatuses = {}
    for (const shelf of shelves) {
      const adapter = platformAdapters[shelf.platform_type]
      const shelfList = platformShelfLists[shelf.platform_type]

      if (!adapter || !shelfList) {
        console.log(`平台 ${shelf.platform_type} 适配器或货架列表不可用`)
        continue
      }

      try {
        // 从已获取的列表中查找货架状态
        const currentStatus = await adapter.getShelfStatusFromList(shelf.platform_shelf_id, shelfList)
        currentStatuses[shelf.platform_type] = currentStatus
        // 更新本地货架状态
        await updateShelfStatus(shelf._id, currentStatus)
        await logger.log({
          user_id: userId,
          platform_type: shelf.platform_type,
          platform_shelf_id: shelf.platform_shelf_id,
          game_account: gameAccount,
          action: 'sync',
          status: 1,
          message: `更新成功，状态: ${getStateText(currentStatus.unified_state)}`,
          trigger_type: 'auto'
        })
      } catch (error) {
        console.error(`更新货架状态失败 ${shelf.platform_type}:`, error)
        await logger.log({
          user_id: userId,
          platform_type: shelf.platform_type,
          platform_shelf_id: shelf.platform_shelf_id,
          game_account: gameAccount,
          action: 'sync',
          status: 0,
          message: error.message,
          trigger_type: 'auto'
        })
      }
    }
    // 检查是否有账号出租，执行联动下架
    await handleRentStatusChange(userId, gameAccount, shelves, currentStatuses, platformAdapters, logger)
  } catch (error) {
    console.error(`处理账号 ${gameAccount} 货架失败:`, error)
  }
}
/**
 * 处理租赁状态变化，执行联动上下架
 * @param {string} userId 用户ID
 * @param {string} gameAccount 游戏账号
 * @param {Array} shelves 货架列表
 * @param {Object} currentStatuses 当前状态
 * @param {Object} platformAdapters 平台适配器
 * @param {Logger} logger 日志记录器
 */
async function handleRentStatusChange(userId, gameAccount, shelves, currentStatuses, platformAdapters, logger) {
  try {
    // 检查是否有平台账号正在出租
    let hasRentedAccount = false
    let rentedPlatform = null
    for (const [platformType, status] of Object.entries(currentStatuses)) {
      if (status && status.unified_state === 1) { // 出租中
        hasRentedAccount = true
        rentedPlatform = platformType
        break
      }
    }
    if (hasRentedAccount) {
      console.log(`账号 ${gameAccount} 在平台 ${rentedPlatform} 出租中，执行其他平台下架`)
      // 下架其他平台的同一账号
      for (const shelf of shelves) {
        if (shelf.platform_type === rentedPlatform) {
          continue // 跳过出租平台
        }
        const adapter = platformAdapters[shelf.platform_type]
        if (!adapter) continue
        try {
          const currentStatus = currentStatuses[shelf.platform_type]
          if (currentStatus && currentStatus.unified_state === 0) { // 当前是待租状态，需要下架
            await adapter.offShelf(shelf.platform_shelf_id)
            await logger.log({
              user_id: userId,
              platform_type: shelf.platform_type,
              platform_shelf_id: shelf.platform_shelf_id,
              game_account: gameAccount,
              action: 'off_shelf',
              status: 1,
              message: `联动下架成功，因为在${rentedPlatform}平台出租中`,
              trigger_type: 'auto'
            })
          }
        } catch (error) {
          console.error(`联动下架失败 ${shelf.platform_type}:`, error)
          await logger.log({
            user_id: userId,
            platform_type: shelf.platform_type,
            platform_shelf_id: shelf.platform_shelf_id,
            game_account: gameAccount,
            action: 'off_shelf',
            status: 0,
            message: error.message,
            trigger_type: 'auto'
          })
        }
      }
    } else {
      console.log(`账号 ${gameAccount} 没有出租，检查是否需要重新上架`)
      // 检查是否有下架的货架需要重新上架
      for (const shelf of shelves) {
        const adapter = platformAdapters[shelf.platform_type]
        if (!adapter) continue
        try {
          const currentStatus = currentStatuses[shelf.platform_type]
          if (currentStatus && currentStatus.unified_state === -1) { // 当前是下架状态，需要上架
            await adapter.onShelf(shelf.platform_shelf_id)
            await logger.log({
              user_id: userId,
              platform_type: shelf.platform_type,
              platform_shelf_id: shelf.platform_shelf_id,
              game_account: gameAccount,
              action: 'on_shelf',
              status: 1,
              message: '自动重新上架成功',
              trigger_type: 'auto'
            })
          }
        } catch (error) {
          console.error(`自动上架失败 ${shelf.platform_type}:`, error)
          await logger.log({
            user_id: userId,
            platform_type: shelf.platform_type,
            platform_shelf_id: shelf.platform_shelf_id,
            game_account: gameAccount,
            action: 'on_shelf',
            status: 0,
            message: error.message,
            trigger_type: 'auto'
          })
        }
      }
    }
  } catch (error) {
    console.error('处理租赁状态变化失败:', error)
  }
}
/**
 * 更新货架状态
 * @param {string} shelfId 货架ID
 * @param {Object} statusData 状态数据
 */
async function updateShelfStatus(shelfId, statusData) {
  try {
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    const dbManager = new DatabaseManager()
    await dbManager.updateShelfStatus(shelfId, statusData)
  } catch (error) {
    console.error('更新货架状态失败:', error)
  }
}
/**
 * 清理过期日志（保留1天）
 */
async function cleanupExpiredLogs() {
  try {
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    const dbManager = new DatabaseManager()
    await dbManager.cleanupExpiredLogs(1)
  } catch (error) {
    console.error('清理过期日志失败:', error)
  }
}